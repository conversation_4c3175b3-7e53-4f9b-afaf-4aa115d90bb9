<script setup lang="ts" name="Dictionary">
import { ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { AddDictionaryApi, DeleteDictionaryApi, GetDictionaryApi, GetDictionaryListApi, UpdateDictionaryApi } from '@/api/system'
import { formatDate } from '@/common/format'
import StatusTag from '@/components/StatusTag/index.vue'
import { deleteToastWithRiskWarning } from '@/common/util'
import FildCard from '@/components/FildCard.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'

const router = useRouter()

// 查询
const { fetchData: fetchDataList, data: dataList, success: listSuccess, total, page, size, handleSizeChange, handleCurrentChange } = GetDictionaryListApi()
const formData = ref<any>({
  name: null,
  type: null,
  status: true,
  desc: null,
})
const rules = ref({
  name: [
    {
      required: true,
      message: '请输入名称',
      trigger: 'blur',
    },
  ],
  type: [
    {
      required: true,
      message: '请输入编号',
      trigger: 'blur',
    },
  ],
  desc: [
    {
      required: true,
      message: '请输入描述',
      trigger: 'blur',
    },
  ],
})

const tableData = ref<any>([])
const searchInfo = ref<any>({})

// 监听 searchInfo 变化，自动调用 getTableData
watch(searchInfo, () => {
  page.value = 1
  if (searchInfo.value.status === '')
    searchInfo.value.status = null
  getTableData()
}, { deep: true })

async function getTableData() {
  await fetchDataList({
    ...searchInfo.value,
  })
  if (listSuccess.value) {
    tableData.value = dataList.value.list
    total.value = dataList.value.total as number
    page.value = dataList.value.page
  }
}

getTableData()

function toDetail(row: any) {
  router.push({
    name: 'DictionaryDetail',
    params: {
      id: row.id,
    },
  })
}

const { fetchData: fetchDataInfo, data: dataInfo, success: infoSuccess } = GetDictionaryApi()
const dialogFormVisible = ref(false)
const type = ref('')
async function updateDictionaryFunc(row: any) {
  await fetchDataInfo({ id: row.id, status: row.status })
  type.value = 'update'
  if (infoSuccess.value) {
    formData.value = dataInfo.value
    dialogFormVisible.value = true
  }
}
function closeDialog() {
  dialogFormVisible.value = false
  formData.value = {
    name: null,
    type: null,
    status: true,
    desc: null,
  }
}

const { fetchData: fetchDataDelete, success: deleteSuccess } = DeleteDictionaryApi()
const { fetchData: fetchDataAdd, success: addSuccess } = AddDictionaryApi()
const { fetchData: fetchDataUpdate, success: updateSuccess } = UpdateDictionaryApi()
async function deleteDictionaryFunc(row: any) {
  const res = await deleteToastWithRiskWarning(row.name)
  if (res) {
    row.visible = false
    await fetchDataDelete({ id: row.id })
    if (deleteSuccess.value) {
      ElMessage({
        type: 'success',
        message: '删除成功',
      })
      if (tableData.value.length === 1 && page.value > 1)
        page.value--
      getTableData()
    }
  }
}

const dialogForm = ref<any>(null)
async function enterDialog() {
  dialogForm.value.validate(async (valid: any) => {
    if (!valid)
      return
    const data = { ...formData.value }
    let success = false
    switch (type.value) {
      case 'create':
        await fetchDataAdd(data)
        success = addSuccess.value
        break
      case 'update':
        await fetchDataUpdate(data)
        success = updateSuccess.value
        break
      default:
        await fetchDataAdd(data)
        break
    }
    if (success) {
      ElMessage.success('操作成功')
      closeDialog()
      getTableData()
    }
  })
}
function openDialog() {
  type.value = 'create'
  dialogFormVisible.value = true
}
</script>

<template>
  <div class="list-age">
    <FildCard title="" :tool-bar="false">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="名称:">
          <template #content>
            <el-input v-model="searchInfo.name" placeholder="搜索条件" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="编号:">
          <template #content>
            <el-input v-model="searchInfo.type" placeholder="搜索条件" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="状态:">
          <template #content>
            <el-select v-model="searchInfo.status" clear placeholder="请选择">
              <el-option :key="1" label="是" :value="1" />
              <el-option :key="2" label="否" :value="2" />
            </el-select>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="描述:">
          <template #content>
            <el-input v-model="searchInfo.desc" placeholder="搜索条件" />
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" :tool-bar="false">
      <template class="gva-btn-list">
        <el-button type="primary" icon="plus" @click="openDialog">
          新增
        </el-button>
      </template>
      <el-table :data="tableData" style="width: 100%" tooltip-effect="dark" row-key="ID">
        <el-table-column type="selection" width="55" />
        <el-table-column align="left" label="名称" prop="name" width="160" />

        <el-table-column align="left" label="编号" prop="type" width="120" />

        <el-table-column align="left" label="状态" prop="status" width="120">
          <template #default="scope">
            <StatusTag :status="scope.row.status" :name="scope.row.status_name" />
          </template>
        </el-table-column>

        <el-table-column align="left" label="描述" prop="desc" width="280" />
        <el-table-column align="left" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.create_time) }}
          </template>
        </el-table-column>
        <el-table-column align="left" label="按钮组">
          <template #default="scope">
            <el-button icon="document" type="primary" link @click="toDetail(scope.row)">
              详情
            </el-button>
            <el-button icon="edit" type="primary" link @click="updateDictionaryFunc(scope.row)">
              变更
            </el-button>
            <!--            <el-popover v-model="scope.row.visible" placement="top" width="160"> -->
            <!--              <p>确定要删除吗？</p> -->
            <!--              <div style="text-align: right; margin-top: 8px"> -->
            <!--                <el-button type="primary" link @click="scope.row.visible = false"> -->
            <!--                  取消 -->
            <!--                </el-button> -->
            <!--                <el-button type="primary" @click="deleteDictionaryFunc(scope.row)"> -->
            <!--                  确定 -->
            <!--                </el-button> -->
            <!--              </div> -->
            <!--              <template #reference> -->
            <!--                <el-button type="primary" link icon="delete" style="margin-left: 10px" @click="scope.row.visible = true"> -->
            <!--                  删除 -->
            <!--                </el-button> -->
            <!--              </template> -->
            <!--            </el-popover> -->
            <el-button type="danger" link icon="delete" style="margin-left: 10px" @click.stop="deleteDictionaryFunc(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="gva-pagination mt-3">
        <el-pagination
          :current-page="page"
          :page-size="size"
          :page-sizes="[10, 30, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </FildCard>
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" title="弹窗操作">
      <el-form ref="dialogForm" :model="formData" :rules="rules" label-width="110px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入名称" clearable :style="{ width: '100%' }" />
        </el-form-item>
        <el-form-item label="编号" prop="type">
          <el-input v-model="formData.type" placeholder="请输入编号" clearable :style="{ width: '100%' }" />
        </el-form-item>
        <el-form-item label="状态" prop="status" required>
          <el-switch v-model="formData.status" active-text="开启" :active-value="1" :inactive-value="2" inactive-text="禁用" />
        </el-form-item>
        <el-form-item label="描述" prop="desc">
          <el-input v-model="formData.desc" placeholder="请输入描述" clearable :style="{ width: '100%' }" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">
            取 消
          </el-button>
          <el-button type="primary" @click="enterDialog">
            确 定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style></style>
