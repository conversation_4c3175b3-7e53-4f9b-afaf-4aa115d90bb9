<script setup lang="ts" name="Dictionary">
import { ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { AddDictionaryApi, DeleteDictionaryApi, GetDictionaryApi, GetDictionaryListApi, UpdateDictionaryApi } from '@/api/system'
import { formatDate } from '@/common/format'
import StatusTag from '@/components/StatusTag/index.vue'
import { deleteToastWithRiskWarning } from '@/common/util'
import Table from '@/components/Table.vue'
import type { TableColumn, TableColumnType } from '@/components/Table/type'
import FildCard from '@/components/FildCard.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'

const router = useRouter()

// 查询
const { fetchData: fetchDataList, data: dataList, success: listSuccess, total, page, size, loading, handleSizeChange, handleCurrentChange } = GetDictionaryListApi()
const formData = ref<any>({
  name: null,
  type: null,
  status: true,
  desc: null,
})
const rules = ref({
  name: [
    {
      required: true,
      message: '请输入名称',
      trigger: 'blur',
    },
  ],
  type: [
    {
      required: true,
      message: '请输入编号',
      trigger: 'blur',
    },
  ],
  desc: [
    {
      required: true,
      message: '请输入描述',
      trigger: 'blur',
    },
  ],
})

const tableData = ref<any>([])
const searchInfo = ref<any>({})

// 监听 searchInfo 变化，自动调用 getTableData
watch(searchInfo, () => {
  page.value = 1
  if (searchInfo.value.status === '')
    searchInfo.value.status = null
  getTableData()
}, { deep: true })

// Table组件配置
const tableConfig = ref({
  loading,
  showPagition: true,
  showOperate: true,
  operateWidth: '200',
  height: '100%',
  page,
  size,
  total,
  handleSizeChange,
  handleCurrentChange,
})

// 表格列配置
const columnList = ref<TableColumn[]>([
  {
    field: 'name',
    title: '名称',
    align: 'left',
  },
  {
    field: 'type',
    title: '编号',
    align: 'left',
  },
  {
    field: 'status',
    title: '状态',
    align: 'left',
    soltName: 'status',
  },
  {
    field: 'desc',
    title: '描述',
    align: 'left',
  },
  {
    field: 'create_time',
    title: '创建时间',
    align: 'left',
    soltName: 'create_time',
  },
])

// 更新表格配置中的分页信息
watch([page, size, total], () => {
  tableConfig.value.page = page.value
  tableConfig.value.size = size.value
  tableConfig.value.total = total.value
})

async function getTableData() {
  tableConfig.value.loading = true
  await fetchDataList({
    ...searchInfo.value,
  })
  if (listSuccess.value) {
    tableData.value = dataList.value.list
    total.value = dataList.value.total as number
    page.value = dataList.value.page
  }
  tableConfig.value.loading = false
}

getTableData()

function toDetail(row: any) {
  router.push({
    name: 'DictionaryDetail',
    params: {
      id: row.id,
    },
  })
}

const { fetchData: fetchDataInfo, data: dataInfo, success: infoSuccess } = GetDictionaryApi()
const dialogFormVisible = ref(false)
const type = ref('')
async function updateDictionaryFunc(row: any) {
  await fetchDataInfo({ id: row.id, status: row.status })
  type.value = 'update'
  if (infoSuccess.value) {
    formData.value = dataInfo.value
    dialogFormVisible.value = true
  }
}
function closeDialog() {
  dialogFormVisible.value = false
  formData.value = {
    name: null,
    type: null,
    status: true,
    desc: null,
  }
}

const { fetchData: fetchDataDelete, success: deleteSuccess, msg: deleteMsg, loading: deleteLoading } = DeleteDictionaryApi()
const { fetchData: fetchDataAdd, success: addSuccess, msg: addMsg } = AddDictionaryApi()
const { fetchData: fetchDataUpdate, success: updateSuccess, msg: updateMsg } = UpdateDictionaryApi()

// 弹窗确定按钮loading状态
const dialogLoading = ref(false)

async function deleteDictionaryFunc(row: any) {
  console.log('删除前 deleteLoading:', deleteLoading.value)

  const res = await deleteToastWithRiskWarning(row.name, {
    loadingRef: deleteLoading,
  })

  console.log('确认删除后 deleteLoading:', deleteLoading.value)

  if (res) {
    try {
      row.visible = false
      console.log('开始调用API deleteLoading:', deleteLoading.value)
      await fetchDataDelete({ id: row.id })
      console.log('API调用完成 deleteLoading:', deleteLoading.value)
      if (deleteSuccess.value) {
        ElMessage({
          type: 'success',
          message: '删除成功',
        })
        if (tableData.value.length === 1 && page.value > 1)
          page.value--
        getTableData()
      }
      else {
        ElMessage.error(deleteMsg.value)
      }
    }
    finally {
      // loading状态由useRequest自动管理
      console.log('finally deleteLoading:', deleteLoading.value)
    }
  }
}

const dialogForm = ref<any>(null)
async function enterDialog() {
  dialogForm.value.validate(async (valid: any) => {
    if (!valid)
      return

    dialogLoading.value = true
    try {
      const data = { ...formData.value }
      let success = false
      let msg = ''
      switch (type.value) {
        case 'create':
          await fetchDataAdd(data)
          success = addSuccess.value
          msg = addMsg.value
          break
        case 'update':
          await fetchDataUpdate(data)
          success = updateSuccess.value
          msg = updateMsg.value
          break
        default:
          await fetchDataAdd(data)
          msg = addMsg.value
          break
      }
      if (success) {
        ElMessage.success('操作成功')
        closeDialog()
        getTableData()
      }
      else {
        ElMessage.error(msg)
      }
    }
    finally {
      dialogLoading.value = false
    }
  })
}
function openDialog() {
  type.value = 'create'
  dialogFormVisible.value = true
}
</script>

<template>
  <div class="list-page">
    <FildCard title="" :tool-bar="false">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="名称:">
          <template #content>
            <el-input v-model="searchInfo.name" placeholder="搜索条件" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="编号:">
          <template #content>
            <el-input v-model="searchInfo.type" placeholder="搜索条件" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="状态:">
          <template #content>
            <el-select v-model="searchInfo.status" clear placeholder="请选择">
              <el-option :key="1" label="是" :value="1" />
              <el-option :key="2" label="否" :value="2" />
            </el-select>
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="描述:">
          <template #content>
            <el-input v-model="searchInfo.desc" placeholder="搜索条件" />
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" class="table-card-full" :tool-bar="false">
      <template #right-top>
        <el-button type="primary" icon="plus" @click="openDialog">
          新增
        </el-button>
      </template>
      <Table
        :config="tableConfig"
        :table-list="tableData"
        :column-list="columnList"
      >
        <template #status="{ row }">
          <StatusTag :status="row.status" :name="row.status_name" />
        </template>
        <template #create_time="{ row }">
          {{ formatDate(row.create_time) }}
        </template>
        <template #operate="{ row }">
          <el-button icon="document" type="primary" link @click="toDetail(row)">
            详情
          </el-button>
          <el-button icon="edit" type="primary" link @click="updateDictionaryFunc(row)">
            变更
          </el-button>
          <el-button type="danger" link icon="delete" style="margin-left: 10px" :loading="deleteLoading" @click.stop="deleteDictionaryFunc(row)">
            删除
          </el-button>
        </template>
      </Table>
    </FildCard>
    <vxe-modal
      v-model="dialogFormVisible"
      show-footer
      title="弹窗操作"
      width="600"
      height="auto"
      :mask="false"
      :lock-view="false"
      :esc-closable="true"
      resize
      @close="closeDialog"
    >
      <el-form ref="dialogForm" :model="formData" :rules="rules" label-width="110px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入名称" clearable :style="{ width: '100%' }" />
        </el-form-item>
        <el-form-item label="编号" prop="type">
          <el-input v-model="formData.type" placeholder="请输入编号" clearable :style="{ width: '100%' }" />
        </el-form-item>
        <el-form-item label="状态" prop="status" required>
          <el-switch v-model="formData.status" active-text="开启" :active-value="1" :inactive-value="2" inactive-text="禁用" />
        </el-form-item>
        <el-form-item label="描述" prop="desc">
          <el-input v-model="formData.desc" placeholder="请输入描述" clearable :style="{ width: '100%' }" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="closeDialog">
          取 消
        </el-button>
        <el-button type="primary" :loading="dialogLoading" @click="enterDialog">
          确 定
        </el-button>
      </template>
    </vxe-modal>
  </div>
</template>

<style></style>
