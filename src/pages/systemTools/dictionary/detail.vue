<script setup lang="ts" name="DictionaryDetail">
import { ElMessage } from 'element-plus'
import { ref, watch } from 'vue'
import { onBeforeRouteUpdate, useRoute } from 'vue-router'
import { formatBoolean, formatDate } from '@/common/format'
import { AddDictionaryDetailApi, DelDictionaryDetailApi, GetDictionaryDetailApi, GetDictionaryDetailListApi, UpdateDictionaryDetailApi } from '@/api/system'
import { deepClone } from '@/common/util'
import Table from '@/components/Table.vue'
import type { TableColumn, TableColumnType } from '@/components/Table/type'
import FildCard from '@/components/FildCard.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import StatusTag from '@/components/StatusTag/index.vue'

const route = useRoute()

const searchInfo = ref<any>({ dictionary_id: Number(route.params.id) })
onBeforeRouteUpdate((to) => {
  if (to.name === 'dictionaryDetail') {
    searchInfo.value.dictionary_id = to.params.id
    getTableData()
  }
})

const formData = ref<any>({
  label: null,
  value: null,
  status: 1,
  sort: null,
})
const rules = ref({
  label: [
    {
      required: true,
      message: '请输入展示值',
      trigger: 'blur',
    },
  ],
  value: [
    {
      required: true,
      message: '请输入字典值',
      trigger: 'blur',
    },
  ],
  sort: [
    {
      required: true,
      message: '排序标记',
      trigger: 'blur',
    },
  ],
  code: [
    {
      required: true,
      message: '编号不能为空',
      trigger: 'blur',
    },
  ],
})

const tableData = ref<any>([])

// 查询
const { fetchData: fetchDataList, data: dataList, success: listSuccess, total, page, size, loading, handleSizeChange, handleCurrentChange } = GetDictionaryDetailListApi()

// 监听 searchInfo 变化，自动调用 getTableData
watch(searchInfo, () => {
  page.value = 1
  if (searchInfo.value.status === '')
    searchInfo.value.status = null
  getTableData()
}, { deep: true })

// Table组件配置
const tableConfig = ref({
  loading,
  showPagition: true,
  showOperate: true,
  operateWidth: '200',
  height: '100%',
  page,
  size,
  total,
  handleSizeChange,
  handleCurrentChange,
})

// 表格列配置
const columnList = ref<TableColumn[]>([
  {
    field: 'code',
    title: '编号',
    align: 'left',
  },
  {
    field: 'label',
    title: '展示值',
    align: 'left',
  },
  {
    field: 'value',
    title: '字典值',
    align: 'left',
  },
  {
    field: 'status',
    title: '启用状态',
    align: 'left',
    soltName: 'status',
  },
  {
    field: 'sort',
    title: '排序标记',
    align: 'left',
  },
  {
    field: 'create_time',
    title: '创建时间',
    align: 'left',
    soltName: 'create_time',
  },
  {
    field: 'remark',
    title: '备注',
    align: 'left',
  },
])

// 更新表格配置中的分页信息
watch([page, size, total], () => {
  tableConfig.value.page = page.value
  tableConfig.value.size = size.value
  tableConfig.value.total = total.value
})

async function getTableData() {
  tableConfig.value.loading = true
  await fetchDataList({
    ...searchInfo.value,
  })
  if (listSuccess.value) {
    tableData.value = dataList.value.list
    total.value = dataList.value.total as number
  }
  tableConfig.value.loading = false
}

getTableData()

const { fetchData: fetchDataInfo, data: dataInfo, success: infoSuccess } = GetDictionaryDetailApi()
const type = ref('')
const dialogFormVisible = ref(false)
async function updateSysDictionaryDetailFunc(row: any) {
  await fetchDataInfo({ id: row.id })
  type.value = 'update'
  if (infoSuccess.value) {
    formData.value = dataInfo.value
    dialogFormVisible.value = true
  }
}

function closeDialog() {
  dialogFormVisible.value = false
  formData.value = {
    label: null,
    value: null,
    status: true,
    sort: null,
    dictionary_id: '',
  }
}

// const { fetchData: fetchDataDel, success: delSuccess } = DelDictionaryDetailApi()
// async function deleteSysDictionaryDetailFunc(row: any) {
//   row.visible = false
//   await fetchDataDel({ id: row.id })
//   if (delSuccess.value) {
//     ElMessage({
//       type: 'success',
//       message: '删除成功',
//     })
//     if (tableData.value.length === 1 && page.value > 1)
//       page.value--
//     getTableData()
//   }
// }

const { fetchData: fetchDataUpdate, success: updateSuccess, msg: updateMsg } = UpdateDictionaryDetailApi()
const { fetchData: fetchDataAdd, success: addSuccess, msg: addMsg } = AddDictionaryDetailApi()

// 弹窗确定按钮loading状态
const dialogLoading = ref(false)

// 行的禁用/启用
async function changeRowStatus(row: any) {
  const params = deepClone(row)
  params.status = row.status === 1 ? 2 : 1
  await fetchDataUpdate(params)
  if (updateSuccess.value) {
    ElMessage({
      type: 'success',
      message: '操作成功',
    })
    closeDialog()
    getTableData()
  }
  else {
    ElMessage.error(updateMsg.value)
  }
}

const dialogForm = ref<any>(null)
async function enterDialog() {
  formData.value.dictionary_id = Number(route.params.id)
  dialogForm.value.validate(async (valid: any) => {
    if (!valid)
      return

    dialogLoading.value = true
    try {
      let success = false
      let msg = ''
      const data = { ...formData.value }
      switch (type.value) {
        case 'create':
          await fetchDataAdd(data)
          success = addSuccess.value
          msg = addMsg.value
          break
        case 'update':
          await fetchDataUpdate(data)
          success = updateSuccess.value
          msg = updateMsg.value
          break
        default:
          await fetchDataAdd(data)
          success = addSuccess.value
          msg = addMsg.value
          break
      }
      if (success) {
        ElMessage({
          type: 'success',
          message: '创建/更改成功',
        })
        closeDialog()
        getTableData()
      }
      else {
        ElMessage.error(msg)
      }
    }
    finally {
      dialogLoading.value = false
    }
  })
}
function openDialog() {
  type.value = 'create'
  dialogFormVisible.value = true
}
</script>

<template>
  <div class="list-page">
    <FildCard title="" :tool-bar="false">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="展示值:">
          <template #content>
            <el-input v-model="searchInfo.label" placeholder="搜索条件" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="字典值:">
          <template #content>
            <el-input v-model="searchInfo.value" type="number" placeholder="搜索条件" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="启用状态:">
          <template #content>
            <el-select v-model="searchInfo.status" placeholder="请选择">
              <el-option key="1" label="是" :value="1" />
              <el-option key="2" label="否" :value="2" />
            </el-select>
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" class="table-card-full" :tool-bar="false">
      <template #right-top>
        <el-button v-has="'Dictionary_add'" type="primary" icon="plus" @click="openDialog">
          新增字典项
        </el-button>
      </template>
      <Table
        :config="tableConfig"
        :table-list="tableData"
        :column-list="columnList"
      >
        <template #create_time="{ row }">
          {{ formatDate(row.create_time) }}
        </template>
        <template #status="{ row }">
          <StatusTag :status="row.status" :name="row.status_name" />
        </template>
        <template #operate="{ row }">
          <el-button type="primary" link icon="edit" @click="updateSysDictionaryDetailFunc(row)">
            变更
          </el-button>
          <el-button type="primary" link @click="changeRowStatus(row)">
            {{ row.status === 1 ? '禁用' : '启用' }}
          </el-button>
        </template>
      </Table>
    </FildCard>

    <vxe-modal
      v-model="dialogFormVisible"
      show-footer
      title="弹窗操作"
      width="600"
      height="auto"
      :mask="false"
      :lock-view="false"
      :esc-closable="true"
      resize
      @close="closeDialog"
    >
      <el-form ref="dialogForm" :model="formData" :rules="rules" label-width="110px">
        <el-form-item label="编号" prop="code">
          <el-input v-model="formData.code" placeholder="请输入编号" clearable :style="{ width: '100%' }" />
        </el-form-item>
        <el-form-item label="展示值" prop="label">
          <el-input v-model="formData.label" placeholder="请输入展示值" clearable :style="{ width: '100%' }" />
        </el-form-item>
        <el-form-item label="字典值" prop="value">
          <el-input-number v-model.number="formData.value" step-strictly :step="1" placeholder="请输入字典值" clearable :style="{ width: '100%' }" />
        </el-form-item>
        <el-form-item label="启用状态" prop="status" required>
          <el-switch v-model="formData.status" active-text="开启" :active-value="1" :inactive-value="2" inactive-text="禁用" />
        </el-form-item>
        <el-form-item label="排序标记" prop="sort">
          <el-input-number v-model.number="formData.sort" placeholder="排序标记" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="formData.remark" type="textarea" placeholder="备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="closeDialog">
          取 消
        </el-button>
        <el-button type="primary" :loading="dialogLoading" @click="enterDialog">
          确 定
        </el-button>
      </template>
    </vxe-modal>
  </div>
</template>

<style></style>
