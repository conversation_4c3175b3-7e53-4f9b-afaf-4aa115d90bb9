import { ElIcon, ElMessage, ElMessageBox } from 'element-plus'
import { Check, WarningFilled } from '@element-plus/icons-vue'

import type { Ref } from 'vue'
import { effectScope, h, watch } from 'vue'
import { IMG_CND_Prefix } from './constant'
import { sumNum } from '@/common/format'

/**
 * 防抖
 * @param {*} fn
 * @param {*} delay
 * @param {*} immediate 是否立即执行第一次
 * @returns
 */
export function debounce(fn: (...val: any) => void, delay: number) {
  let timer: NodeJS.Timeout | null = null
  return (...param: any[]) => {
    if (timer)
      clearTimeout(timer)
    timer = setTimeout(() => {
      fn(...param)
    }, delay)
  }
}

/**
 * 节流
 * @param {*} fn
 * @param {*} delay
 * @returns
 */
export function throttle(fn: (...val: any) => void, delay: number) {
  let pre = 0
  return (...params: any[]) => {
    const now = new Date().getTime()
    if (now - pre > delay) {
      fn(...params)
      pre = now
    }
  }
}

/**
 * 数组分页 返回总页数/ 分页后的数组
 * @param {*} pageNo
 * @param {*} pageSize
 * @param {*} array
 * @returns
 */
export function pagination(pageNo: number, pageSize: number, array: any[]) {
  const offset = (pageNo - 1) * pageSize
  const totalPage = Math.ceil(array.length / pageSize)
  const result
    = offset + pageSize >= array.length
      ? array.slice(offset, array.length)
      : array.slice(offset, offset + pageSize)
  return {
    result,
    totalPage,
  }
}
/**
 * 批量过滤对象值为空的属性
 * @param {object} val 需要过滤的对象
 * @param {Array}  arr 排除过滤的属性
 * @returns
 */
export function getFilterData(val: Record<string, any> = {}, arr: any[] = []) {
  const res: Record<string, any> = {}
  for (const key in val) {
    if ((val[key] || val[key] === 0) && !arr.includes(key))
      res[key] = val[key]
  }

  return res
}
/**
 * 判断数据是否是数字
 * @param {object} val 需要判断的数据
 * @returns
 */
export function filterNumber(val: string | number): boolean {
  if (/^-?[0-9]+.?[0-9]*/.test(val as string))
    return true
  else return false
}

/**
 * 文件类型过滤器
 * @param {Array} fileList 混杂了所有file的url的数组
 */
export function fileFilter(fileList: any[]) {
  const formatedFileList = fileList.map(file => formatUrl(file))

  function checkFile(fileValue: string) {
    const index = fileValue.lastIndexOf('.') // （考虑严谨用lastIndexOf(".")得到）得到"."在第几位
    const fileValueSuffix = fileValue.substring(index) // 截断"."之前的，得到后缀
    if (/(.*)\.(mp4|avi|wmv|MP4|AVI|WMV)$/.test(fileValueSuffix)) {
      // 根据后缀，判断是否符合视频格式
      return 'video'
    }
    else if (
      /(.*)\.(jpg|JPG|bmp|BMP|mpg|MPG|mpeg|MPEG|tis|TIS|webp|jfif)$/.test(
        fileValueSuffix,
      )
    ) {
      // 根据后缀，判断是否符合图片格式
      return 'image'
    }
    else if (
      /(.*)\.(xls|XLS|xlsx|XLSX|doc|DOC|docx|DOCX|pdf|PDF|PPT|PPTX|ppt|pptx)$/.test(
        fileValueSuffix,
      )
    ) {
      // 根据后缀，判断是否符合OFFICE格式
      return 'office'
    }
    return ''
  }

  const imageList: string[] = []
  const videoList: string[] = []
  const officeList: string[] = []
  const filterMap = (fileType: string, url: string) => {
    return (
      {
        image: () => imageList.push(url),
        video: () => videoList.push(url),
        office: () => officeList.push(url),
      }[fileType] || false
    )
  }

  let currentFileUrl
  const ls = formatedFileList.length
  let handlerFunc: ((...val: any) => void) | boolean
  for (let i = ls - 1; i >= 0; i--) {
    currentFileUrl = formatedFileList[i]
    if (filterMap) {
      handlerFunc = filterMap(checkFile(currentFileUrl), currentFileUrl)
      handlerFunc && handlerFunc()
    }
  }

  return {
    imageList,
    videoList,
    officeList,
  }
}
// 匹配前缀 例如：//xxx.com  或者 http://xxx.com 或者 https://xxx.com

const URL_REGEXP
  = /(https?:)?\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()!@:%_\+.~#?&=]*)/gi
/**
 * 判断是否有前缀
 * @param {string} url url路径
 */
export const withBaseUrl = function (url: string) {
  return URL_REGEXP.test(url)
}
/**
 * 去除前缀 //xxx.com  或者 http://xxx.com 或者 https://xxx.com
 * @param {string} url url路径
 */
export function breakupUrl(url: string) {
  return url?.replace(URL_REGEXP, '') ?? url
}
/**
 * 拼接前缀 http://xxx.com + /asdfsafdas/154531asdf465413.png
 * @param {string} url url路径
 */
export function jointUrl(url: string) {
  return withBaseUrl(url) ? url : `${IMG_CND_Prefix}${url}`
}

export function formatUrl(url: string, mode = '!w600') {
  return withBaseUrl(url) ? url : jointUrl(url) + mode
}
/**
 * 获取cdn的md5前缀 32位
 * @param {string} url cdn 文件路径
 * @returns md5
 */
export function getImageMd5(url: string) {
  const result = url.match(/\/([\d\w]{32})\d{8}.?/)
  if (!result)
    return ''
  return result[1]
}

export function parseToRaw(target: any) {
  return target ? JSON.parse(JSON.stringify(target)) : ''
}

export function isObject(target: any) {
  return Object.prototype.toString.call(target) === '[object Object]'
}
export function isArray(target: any) {
  return Object.prototype.toString.call(target) === '[object Array]'
}
export function isPromise(target: any) {
  return (!!target && typeof target === 'object')
    || (typeof target === 'function' && typeof target?.then === 'function')
}
// url 转 File 对象
export async function getImageFileFromUrl(url: string, imageName: string) {
  return await new Promise((resolve, reject) => {
    let blob = null
    const xhr = new XMLHttpRequest()
    xhr.open('GET', url)
    xhr.setRequestHeader('Accept', 'image/png')
    xhr.responseType = 'blob'
    // 加载时处理
    xhr.onload = () => {
      // 获取返回结果
      blob = xhr.response
      const imgFile = new File([blob], imageName, { type: 'image/png' })
      // 返回结果
      resolve(imgFile)
    }
    xhr.onerror = (e) => {
      reject(e)
    }
    // 发送
    xhr.send()
  })
}

// 删除警告弹窗确认

export async function deleteToast(tips: string) {
  const tip = '此操作将删除，是否继续?'
  return await new Promise((resolve: any, reject: any) => {
    ElMessageBox.confirm(!tips ? tip : tips, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then((res) => {
        if (res)
          resolve(true)
        else reject(res)
      })
      .catch(() => {
        resolve(false)
      })
  })
}
// 风险警告
export async function deleteToastWithRiskWarning(
  deleteTarget: string,
  options?: {
    loadingRef?: { value: boolean }
    confirmButtonLoading?: boolean
  },
) {
  return await new Promise((resolve: any) => {
    ElMessageBox({
      customStyle: '--el-messagebox-width: 600px',
      title: '',
      message: h('div', { class: 'pl-6' }, [
        h('h2', { class: 'flex items-center mb-4 text-xl' }, [h(ElIcon, { color: '#f59a23', class: 'mr-2' }, () => h(WarningFilled)), '基础资料删除风险预警']),
        h('p', null, ['您正在尝试删除基础资料【', h('span', { class: 'text-red-500' }, deleteTarget), '】，此操作将导致：']),
        h('div', { class: ' flex items-center' }, [h('div', { class: 'w-[16px] h-[16px] rounded-full bg-red-500 mr-2' }, null), h('div', null, '业务断层：关联单据（订单/BOM/生产记录）无法追溯')]),
        h('div', { class: 'flex items-center' }, [h('div', { class: 'w-[16px] h-[16px] rounded-full bg-red-500 mr-2' }, null), h('div', null, '系统异常：依赖此数据的流程（如审批流、质检模版）可能报错')]),
        h('div', { class: 'flex items-center' }, [h('div', { class: 'w-[16px] h-[16px] rounded-full bg-red-500 mr-2' }, null), h('div', null, '合规风险：违反ISO9001数据留存要求（删除关键生产资料）')]),
        h('div', { class: 'mb-2' }, ['优先【停用】', h('span', { class: 'text-red-500' }, deleteTarget), '，并保留历史记录（推荐）']),
      ]),
      showCancelButton: true,
      showConfirmButton: true,
      distinguishCancelAndClose: true,
      confirmButtonText: '取消',
      cancelButtonText: '确定风险继续操作',
      confirmButtonLoading: options?.confirmButtonLoading || false,
      callback: (value: string) => {
        // 如果传入了loadingRef，在确认删除时设置loading状态
        if (value === 'cancel' && options?.loadingRef)
          options.loadingRef.value = true

        if (value === 'cancel')
          resolve(true)
        else
          resolve(false)
      },
    })
  })
}
// 风险警告
export async function deleteToastWithRiskWarning2(deleteTarget: string) {
  return await new Promise((resolve: any) => {
    ElMessageBox({
      customStyle: '--el-messagebox-width: 650px',
      title: '',
      message: h('div', { class: 'pl-6' }, [
        h('h2', { class: 'flex items-center mb-4 text-xl' }, [h(ElIcon, { color: '#f59a23', class: 'mr-2' }, () => h(WarningFilled)), '基础资料删除风险预警']),
        h('p', null, ['您正在尝试删除往来单位【', h('span', { class: 'text-red-500' }, deleteTarget), '】，此操作将导致：']),
        h('div', { class: ' flex items-center' }, [h('div', { class: 'w-[16px] h-[16px] rounded-full bg-red-500 mr-2' }, null), h('div', null, '历史数据断链：所有关联订单、合同、交易记录将无法通过该供应商检索')]),
        h('div', { class: 'flex items-center' }, [h('div', { class: 'w-[16px] h-[16px] rounded-full bg-red-500 mr-2' }, null), h('div', null, '生产风险：若该有关联原料、坯布、成品资料，可能导致生产配置参数丢失')]),
        h('div', { class: 'flex items-center' }, [h('div', { class: 'w-[16px] h-[16px] rounded-full bg-red-500 mr-2' }, null), h('div', null, '合规风险：已关联的报关单/退税记录都是可能影响审计')]),
        h('div', { class: '' }, [h(ElIcon, { color: '#333333', class: 'mr-2' }, () => h(Check)), '推荐替代方案：']),
        h('div', { class: 'mb-2' }, ['优先【停用】', h('span', { class: 'text-red-500' }, deleteTarget), '并保留历史记录（推荐）']),
      ]),
      showCancelButton: true,
      showConfirmButton: true,
      distinguishCancelAndClose: true,
      confirmButtonText: '取消',
      cancelButtonText: '确定风险继续操作',
      callback: (value: string) => {
        if (value === 'cancel')
          resolve(true)
        else
          resolve(false)
      },
    })
  })
}

/**
 * 重置筛选内容
 * @returns Object
 * @param obj
 */

export function resetData(obj: any) {
  for (const i in obj) obj[i] = ''

  return obj
}

// 删除需要填写备注
export async function deleteRemark() {
  return await new Promise((resolve: any, _reject: any) => {
    ElMessageBox.prompt('确认删除该记录嘛？', '提示', {
      message: '删除过后不可恢复，请谨慎操作',
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      inputPlaceholder: '请输入删除备注',
      type: 'warning',
    })
      .then(({ value }) => {
        resolve(value)
      })
  })
}

// 禁用提示框
/**
 * @param api: 请求api方法
 * @param row: 当前行（批量时是数组）
 * @param allStatus: 批量时需要传状态
 * @returns
 */
export async function disabledConfirmBox<T extends (val?: any) => any>({
  api,
  row,
  allStatus = 1,
}: {
  api: T
  row: any
  allStatus?: 1 | 2
}) {
  return await new Promise((resolve: any, reject: any) => {
    const { fetchData, success, msg } = api()
    let status_msg = ''
    if (Array.isArray(row))
      status_msg = allStatus === 1 ? '启用' : '禁用'
    else status_msg = row.status === 1 ? '禁用' : '启用'

    ElMessageBox.confirm(`确认${status_msg}该数据？`, '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
    })
      .then(async () => {
        let ids = []
        let status_n = 1
        if (!Array.isArray(row)) {
          ids = [row.id]
          status_n = row.status === 1 ? 2 : 1
        }
        else {
          ids = row.map((item: any) => item.id)
          status_n = allStatus
        }
        await fetchData({ id: ids.join(','), status: status_n })
        if (success.value) {
          ElMessage.success('修改成功')
          resolve()
        }
        else {
          ElMessage.error(msg.value)
          reject(msg.value)
        }
      })
      .catch(() => {
        reject(new Error('取消操作'))
      })
  })
}

// 删除提示框
/**
 * @param api: 请求api方法
 * @param row: 当前行（批量时是数组）
 * @returns
 */
export async function deleteConfirmBox<T extends (val?: any) => any>({
  api,
  row,
}: {
  api: T
  row: any
}) {
  return await new Promise((resolve: any, reject: any) => {
    const { fetchData, success, msg } = api()
    ElMessageBox.confirm(`确认删除该数据？`, '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
    })
      .then(async () => {
        let ids = []
        if (!Array.isArray(row))
          ids = [row.id]
        else ids = row.map((item: any) => item.id)

        await fetchData({ id: ids.join(',') })
        if (success.value) {
          ElMessage.success('删除成功')
          resolve()
        }
        else {
          ElMessage.error(msg.value)
          reject(msg.value)
        }
      })
      .catch(() => {
        reject(new Error('取消操作'))
      })
  })
}

// 订单状态更改
/**
 * @param api: 请求api方法
 * @param id: 需要更改的id
 * @param audit_status: 需要改变的状态
 * @param message: 提示
 * @oaram fn: 成功回调
 * @returns
 */
export async function orderStatusConfirmBox<T extends (val?: any) => any>({
  api,
  id,
  audit_status,
  message,
  fn = () => { },
  order_type = 0,
  params = {},
  loadingRef,
}: {
  api: T
  id: string | number
  audit_status?: number | string
  message: { title: string, desc: string }
  fn?: (data: any) => void
  order_type?: number
  params?: Record<string, any>
  loadingRef?: Ref<boolean>
}) {
  return await new Promise((resolve: any, reject: any) => {
    const { fetchData, data, success, msg, loading, code } = api()
    const scope = effectScope()
    scope.run(() => {
      watch(loading, () => {
        if (loadingRef)
          loadingRef.value = loading.value
      })
    })
    ElMessageBox.confirm(message?.desc, message?.title, {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(async () => {
        await fetchData(
          getFilterData({ id, audit_status, order_type, ...params }),
        )
        if (success.value) {
          ElMessage.success('修改成功')
          fn(data.value)
          resolve()
        }
        else {
          ElMessage.error(msg.value)
          reject({ code, msg: msg.value })
        }
        // 处理掉当前作用域内的所有 effect
        scope.stop()
      })
      .catch(() => {
        reject(new Error('取消操作'))
        // 处理掉当前作用域内的所有 effect
        scope.stop()
      })
  })
}

// 深拷贝
export function deepClone(obj: any) {
  const result: any = typeof obj.splice === 'function' ? [] : {}
  if (obj && typeof obj === 'object') {
    for (const key in obj) {
      if (obj[key] && typeof obj[key] === 'object')
        result[key] = deepClone(obj[key])
      else result[key] = obj[key]
    }
    return result
  }
  return obj
}

// 删除数据指定id，返回删除的数据
/**
 * @param keyFiled: 指定判断字段
 * @param val: 传进需要判断的值
 * @param list: 当前数据、数组
 * @returns
 */
export function deleteById(keyFiled: string, val: any, list: any) {
  for (let index = list.length - 1; index >= 0; index--) {
    if (list[index] && list[index][keyFiled] === val)
      list.splice(index, 1)
  }

  return list
}

// 查询数组是否存在相同的数据
/**
 * @param keyFiled: 指定判断字段
 * @param data: 需要判断的数组
 * @param val: 传进需要判断的值
 * @returns
 */
interface Data {
  keyFiled: any
  val: any

  [key: string]: any
}

export function isIdExisted<T extends Data>(
  keyFiled: any,
  val: any,
  data: T[],
): boolean {
  return data.some(item => item[keyFiled] === val)
}

// 过滤相同值数据
/**
 * @param keyFiled: 指定判断字段
 * @param data: 需要过滤的数组
 * @param val: 传进需要判断的值
 * @returns
 */
export function filterDataList<T extends Data>(
  keyFiled: any,
  val: any,
  data: T[],
): T[] {
  return data.filter(item => item[keyFiled] !== val)
}

// 订单导出
/**
 * @param api: 请求api方法
 * @param filter: 条件
 * @param title: 文件名称
 * @returns
 */
interface ExportListParam {
  api: any
  filterData: object
  title: string
}

export async function exportList({
  api,
  filterData = {},
  title = '',
}: ExportListParam) {
  return await new Promise((resolve: any, reject: any) => {
    const { fetchData, success, msg } = api({ nameFile: title })
    ElMessageBox.confirm('确定导出数据吗？', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
    })
      .then(async () => {
        await fetchData(getFilterData({ ...filterData, download: 1 }))
        if (success.value) {
          ElMessage.success('导出成功')
          resolve()
        }
        else {
          ElMessage.error(msg.value)
          reject(msg.value)
        }
      })
      .catch(() => {
        reject(new Error('取消操作'))
      })
  })
}

/**
 * @returns YYYY-M-D
 */

export function getCurrentDate() {
  const today = new Date()
  const year = today.getFullYear()
  const month = today.getMonth() + 1
  const day = today.getDate()
  return `${year}-${month}-${day}`
}

export function getDefaultSaleSystem() {
  let res: User.Info | null = null
  if (localStorage.getItem('user'))
    res = JSON.parse(localStorage.getItem('user') as string)?.user
  return res
}

/**
 * @param n: 获取当前时间的{{value}}的几月或者几周或者几天   //近三天传入-3，近一个月传入1
 * @returns
 */

export function getRecentDay_Date(n: number) {
  let result = ''
  const datenow = new Date()
  const dateend = `${datenow.getFullYear().toString()}-${(datenow.getMonth() + 1).toString()}-${datenow.getDate().toString()}`
  datenow.setMonth(datenow.getMonth() - n)
  const dyear = datenow.getFullYear()
  let dmonth = datenow.getMonth() + 1
  dmonth = dmonth < 10 ? 0 + dmonth : dmonth
  const dday = datenow.getDate()
  const datestart = `${dyear.toString()}-${dmonth.toString()}-${dday.toString()}`
  result += `${datestart},`
  result += dateend

  return result.split(',')
}

// 已录入完毕（细码）匹数为0 但是 数量为0的情况下也算未录入
// item_fc_data 里面的一般是roll 和 weight 字段
export function isXimaAlreadyEntered(
  row: any,
  { rollKey = 'roll', weightKey = 'weight' },
) {
  const sumWeight = Number(sumNum(row?.item_fc_data, 'weight'))

  const isWeightFinish
    = sumWeight >= row[weightKey] && sumWeight > 0 && row[weightKey] !== ''
  const isRollFinish
    = Number(sumNum(row?.item_fc_data, 'roll')) >= row[rollKey]
    && row[rollKey] !== ''

  return isRollFinish && isWeightFinish
}
// 判断是否是生产环境
export function isProd() {
  return ['kdyb', 'production'].includes(import.meta.env.MODE)
}

// 获取文件名字
export function getFileName(url: string) {
  const arr = url.split('/')
  return arr[arr.length - 1]
}

// 处理文件路径-防止文件名特殊符号打开失败
export function formatFileName(url: string) {
  const lastFileName = getFileName(url)
  const newUrl = url.replace(lastFileName, `${encodeURIComponent(lastFileName)}`)
  return newUrl
}

interface TreeNode {
  id: string | number
  name: string
  children?: TreeNode[]
  [key: string]: any
}

/**
 * 根据目标值和目标名称查找路径
 * @param tree 树结构
 * @param targetValue 目标值
 * @param targetName 目标名称
 * @param childrenKey 子节点键名
 * @returns 路径
 */
export function findNodePath(tree: TreeNode[], targetValue: string | number, targetName: string, childrenKey: string = 'children', lastListName: string = '', lastRoute: any = ''): TreeNode[] {
  const path: TreeNode[] = []

  function dfs(nodes: TreeNode[]): boolean {
    for (const node of nodes) {
      // 将当前节点加入路径
      path.push(node)

      // 找到目标节点
      if (node[targetName] === targetValue)
        return true

      // 如果有子节点，继续递归查找
      if (node[childrenKey]?.length) {
        const found = dfs(node[childrenKey])
        if (found)
          return true
      }

      // 找到最后一级的节点
      if (lastListName && node[lastListName].find((item: any) => item.resource_router_name === targetValue)) {
        if (lastRoute) {
          // 删除最后一级节点
          // path.pop()
          // 添加最后一级节点
          path.push({
            ...lastRoute,
            name: lastRoute.meta.title,
          })
        }
        return true
      }

      // 如果当前分支未找到目标，则从路径中移除当前节点
      path.pop()
    }
    return false
  }

  dfs(tree)
  return path
}

// 带缓存的查找函数
const pathCache = new Map()

/**
 * 根据目标值和目标名称查找路径-带缓存
 * @param tree 树结构
 * @param targetValue 目标值
 * @param targetName 目标名称
 * @param childrenKey 子节点键名
 * @returns 路径
 */
export function findNodePathWithCache(
  tree: TreeNode[],
  targetValue: string | number,
  targetName: string,
  childrenKey: string = 'children',
  lastListName: string = '',
  lastRoute: any = '',
): TreeNode[] {
  const cacheKey = targetValue.toString()

  if (pathCache.has(cacheKey))
    return pathCache.get(cacheKey)

  const result = findNodePath(tree, targetValue, targetName, childrenKey, lastListName, lastRoute)
  pathCache.set(cacheKey, result)

  return result
}

/**
 * 是否为主单位
 * @param item 成品信息
 * @param mainUnitKey  主单位id键
 * @param auxiliaryUnitKey 辅助单位id键
 */
export function isMainUnit(item: any, mainUnitKey = 'measurement_unit_id', auxiliaryUnitKey = 'auxiliary_unit_id') {
  return item[mainUnitKey] === item[auxiliaryUnitKey]
}
export function sleep(time: number) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true)
    }, time)
  })
}
/**
 * 判断是否为 '' 或者 underfined 或者 null
 * @param val
 * @param isZero  0 是否也为空 默认false
 * @returns
 */
export function strIsEmpty(val: string | number, isZero = false) {
  const isEmpty = val === '' || val === undefined || val === null
  if (isZero)
    return isEmpty && Number(val) === 0

  return isEmpty
}

/**
 * 样式读取：数字>0红色，数字<0绿色
 * @param val 数字
 * @param isVal 是否返回颜色值 默认false则返回class, true则返回颜色值
 */
export function getTableCellTextClass(val: number = 0, isVal = false) {
  if (Number(val) > 0)
    return isVal ? '#ed90bd' : 'text-[#ed90bd]'
  else if (Number(val) < 0)
    return isVal ? '#3fc191' : 'text-[#3fc191]'
}

/**
 * 实现浏览器复制功能，兼容 Chrome、夸克、火狐、Edge 等浏览器
 * @param {string} text 要复制的文本内容
 * @returns {Promise<boolean>} 复制成功返回 true，失败返回 false
 */
export async function copyToClipboard(text: string): Promise<boolean> {
// 首先尝试使用 Clipboard API
  if (navigator.clipboard && window.isSecureContext) {
    try {
      await navigator.clipboard.writeText(text)
      ElMessage.success('复制成功') // 新增复制成功提示
      return true
    }
    catch (error) {
      console.error('Clipboard API 复制失败:', error)
    }
  }

  // 如果 Clipboard API 不可用，使用传统的 document.execCommand 方法
  const textarea = document.createElement('textarea')
  textarea.value = text
  textarea.style.position = 'fixed'
  textarea.style.opacity = '0'
  document.body.appendChild(textarea)
  textarea.select()

  try {
    const success = document.execCommand('copy')
    if (success)
      ElMessage.success('复制成功') // 新增复制成功提示

    return success
  }
  catch (error) {
    console.error('document.execCommand 复制失败:', error)
    return false
  }
  finally {
    document.body.removeChild(textarea)
  }
}

/**
 * 实现传入一个字符串，根据截取字段转换为数组
 * @param {string|undefined} str 要处理的字符串
 * @param {string} separator 分隔符，默认为逗号
 * @returns {string[]} 处理后的数组
 */
export function stringToArray(str: string | undefined, separator: string = ','): string[] {
  if (!str)
    return []
  // 兼容传入数组的情况
  if (Array.isArray(str))
    return str
  return str.split(separator)
}

/**
 * 实现传入一个数组，根据分隔符转换为字符串
 * @param {string[]} arr 要处理的数组
 * @param {string} separator 分隔符，默认为逗号
 * @returns {string} 处理后的字符串
 */
export function arrayToString(arr: string[] | string | number[] | undefined, separator: string = ','): string {
  if (!arr || arr.length === 0)
    return ''

  // 兼容传入字符串的情况
  if (typeof arr === 'string')
    return arr

  // 去除空白数据
  arr = arr.filter(item => item !== undefined && item !== null && item !== '')

  return arr.join(separator)
}
